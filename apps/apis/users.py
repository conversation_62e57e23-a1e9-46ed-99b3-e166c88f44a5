#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 18:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : users.py
# @Update  : 2025/8/7 18:00 用户API路由模块

"""
用户API路由模块

该模块定义了用户相关的所有API端点，遵循Context7和FastAPI最佳实践：
- RESTful API设计
- 完整的CRUD操作
- 统一的错误处理
- 详细的API文档
- 类型安全的接口

API端点：
- POST /users/ - 创建用户
- GET /users/ - 获取用户列表
- GET /users/me - 获取当前用户信息
- GET /users/{user_id} - 获取指定用户信息
- PUT /users/{user_id} - 更新用户信息
- DELETE /users/{user_id} - 删除用户
- POST /users/{user_id}/change-password - 修改密码
- GET /users/search - 搜索用户
- GET /users/statistics - 获取用户统计

参考：FastAPI最佳实践和RESTful API设计
"""

from __future__ import annotations

import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from apis.dependencies import (
    get_current_active_user,
    get_current_user,
    get_db_session,
    get_user_query_params,
    require_admin_user,
    valid_owned_user,
    valid_user_id,
)
from models.users import Users
from schemas.users import (
    UserCreate,
    UserUpdate,
    UserResponse,
    UserListResponse,
    UserQuery,
)
from services import user_service
from services.base import ConflictError, NotFoundError, ServiceError, ValidationError

# 创建用户路由器
router = APIRouter(
    prefix="/users",
    tags=["用户管理"],
    responses={
        404: {"description": "用户不存在"},
        401: {"description": "未认证"},
        403: {"description": "权限不足"},
    },
)


@router.post(
    "/",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建用户",
    description="创建新用户账户，需要提供用户名、邮箱和密码",
    responses={
        201: {
            "description": "用户创建成功",
            "model": UserResponse
        },
        400: {
            "description": "请求数据无效",
            "content": {
                "application/json": {
                    "example": {"detail": "用户名已存在"}
                }
            }
        },
        409: {
            "description": "数据冲突",
            "content": {
                "application/json": {
                    "example": {"detail": "邮箱已存在"}
                }
            }
        }
    }
)
async def create_user(
    user_data: UserCreate,
    session: AsyncSession = Depends(get_db_session)
) -> UserResponse:
    """
    创建新用户
    
    - **username**: 用户名，3-20个字符，只能包含字母、数字和下划线
    - **email**: 邮箱地址，必须是有效的邮箱格式
    - **password**: 密码，至少8个字符
    - **confirm_password**: 确认密码，必须与密码一致
    - **comment**: 可选的备注信息
    """
    try:
        user = await user_service.create_user(session, user_data)
        return UserResponse.model_validate(user)
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {e.message}"
        )


@router.get(
    "/",
    response_model=UserListResponse,
    summary="获取用户列表",
    description="获取用户列表，支持分页、排序和筛选",
    responses={
        200: {
            "description": "用户列表获取成功",
            "model": UserListResponse
        }
    }
)
async def list_users(
    query: UserQuery = Depends(get_user_query_params),
    session: AsyncSession = Depends(get_db_session),
    current_user: Users = Depends(get_current_active_user)  # 需要认证才能查看用户列表
) -> UserListResponse:
    """
    获取用户列表
    
    支持以下功能：
    - **分页**: 通过page和size参数控制
    - **筛选**: 通过username和email参数进行模糊搜索
    - **排序**: 通过sort_by和sort_order参数控制排序
    
    需要用户认证才能访问。
    """
    try:
        result = await user_service.get_users(session, query)
        return result
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {e.message}"
        )


@router.get(
    "/me",
    response_model=UserResponse,
    summary="获取当前用户信息",
    description="获取当前认证用户的详细信息",
    responses={
        200: {
            "description": "当前用户信息",
            "model": UserResponse
        }
    }
)
async def get_current_user_info(
    current_user: Users = Depends(get_current_active_user)
) -> UserResponse:
    """
    获取当前用户信息
    
    返回当前认证用户的详细信息，包括：
    - 用户名
    - 邮箱地址
    - 创建时间
    - 更新时间
    - 备注信息
    """
    return UserResponse.model_validate(current_user)


@router.get(
    "/{user_id}",
    response_model=UserResponse,
    summary="获取指定用户信息",
    description="根据用户ID获取用户详细信息",
    responses={
        200: {
            "description": "用户信息获取成功",
            "model": UserResponse
        },
        404: {
            "description": "用户不存在",
            "content": {
                "application/json": {
                    "example": {"detail": "用户ID xxx 不存在"}
                }
            }
        }
    }
)
async def get_user_by_id(
    user: Users = Depends(valid_user_id),
    current_user: Users = Depends(get_current_active_user)  # 需要认证
) -> UserResponse:
    """
    获取指定用户信息
    
    根据用户ID获取用户的详细信息。
    需要用户认证才能访问。
    """
    return UserResponse.model_validate(user)


@router.put(
    "/{user_id}",
    response_model=UserResponse,
    summary="更新用户信息",
    description="更新指定用户的信息，只能更新自己的信息或管理员可以更新任何用户",
    responses={
        200: {
            "description": "用户信息更新成功",
            "model": UserResponse
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {"detail": "权限不足：只能操作自己的资源"}
                }
            }
        },
        409: {
            "description": "数据冲突",
            "content": {
                "application/json": {
                    "example": {"detail": "用户名已存在"}
                }
            }
        }
    }
)
async def update_user(
    user_data: UserUpdate,
    user: Users = Depends(valid_owned_user),
    session: AsyncSession = Depends(get_db_session)
) -> UserResponse:
    """
    更新用户信息
    
    可以更新以下字段：
    - **username**: 新用户名（可选）
    - **email**: 新邮箱地址（可选）
    - **comment**: 新备注信息（可选）
    - **password**: 新密码（可选）
    
    注意：
    - 只能更新自己的信息，除非是管理员
    - 用户名和邮箱必须唯一
    - 密码会自动进行安全哈希处理
    """
    try:
        updated_user = await user_service.update_user(session, user.id, user_data)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse.model_validate(updated_user)
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户失败: {e.message}"
        )


@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除用户",
    description="删除指定用户，需要管理员权限"
)
async def delete_user(
    user_id: uuid.UUID,
    admin: Users = Depends(require_admin_user),
    session: AsyncSession = Depends(get_db_session)
):
    """
    删除用户

    永久删除指定的用户账户。

    **注意**：
    - 需要管理员权限
    - 删除操作不可逆
    - 会删除用户的所有相关数据
    """
    try:
        success = await user_service.delete_user(session, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户ID {user_id} 不存在"
            )

    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除用户失败: {e.message}"
        )


@router.post(
    "/{user_id}/change-password",
    status_code=status.HTTP_200_OK,
    summary="修改用户密码",
    description="修改指定用户的密码，只能修改自己的密码或管理员可以修改任何用户密码",
    responses={
        200: {
            "description": "密码修改成功",
            "content": {
                "application/json": {
                    "example": {"message": "密码修改成功"}
                }
            }
        },
        400: {
            "description": "请求无效",
            "content": {
                "application/json": {
                    "example": {"detail": "旧密码错误"}
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {"detail": "权限不足：只能操作自己的资源"}
                }
            }
        }
    }
)
async def change_user_password(
    user_id: uuid.UUID,
    old_password: str = Query(..., description="旧密码"),
    new_password: str = Query(..., description="新密码"),
    user: Users = Depends(valid_owned_user),
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    修改用户密码

    修改指定用户的密码。

    **参数**：
    - **old_password**: 当前密码
    - **new_password**: 新密码

    **注意**：
    - 只能修改自己的密码，除非是管理员
    - 新密码必须符合强度要求
    - 旧密码必须正确
    """
    try:
        success = await user_service.change_password(
            session, user_id, old_password, new_password
        )

        if success:
            return {"message": "密码修改成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码修改失败"
            )

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改密码失败: {e.message}"
        )


@router.get(
    "/search",
    response_model=List[UserResponse],
    summary="搜索用户",
    description="根据关键词搜索用户",
    responses={
        200: {
            "description": "搜索结果",
            "model": List[UserResponse]
        }
    }
)
async def search_users(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回结果数量限制"),
    current_user: Users = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_db_session)
) -> List[UserResponse]:
    """
    搜索用户

    根据关键词在用户名、邮箱和备注中进行模糊搜索。

    **参数**：
    - **q**: 搜索关键词
    - **limit**: 返回结果数量限制（最大100）

    需要用户认证才能访问。
    """
    try:
        users = await user_service.search_users(session, q, limit=limit)
        return [UserResponse.model_validate(user) for user in users]

    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索用户失败: {e.message}"
        )


@router.get(
    "/statistics",
    response_model=Dict[str, Any],
    summary="获取用户统计信息",
    description="获取用户相关的统计数据，需要管理员权限",
    responses={
        200: {
            "description": "统计信息",
            "content": {
                "application/json": {
                    "example": {
                        "total_users": 1000,
                        "today_new_users": 5,
                        "month_new_users": 150,
                        "statistics_date": "2024-01-01"
                    }
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {"detail": "需要管理员权限"}
                }
            }
        }
    }
)
async def get_user_statistics(
    admin: Users = Depends(require_admin_user),
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    获取用户统计信息

    返回用户相关的统计数据，包括：
    - 总用户数
    - 今日新增用户数
    - 本月新增用户数
    - 统计日期

    **注意**：需要管理员权限才能访问。
    """
    try:
        statistics = await user_service.get_user_statistics(session)
        return statistics

    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {e.message}"
        )
